'use client';

import React, { useState } from 'react';
import Image from 'next/image';
import Link from 'next/link';

interface ProjectProps {
  title: string;
  category: string;
  location: string;
  imageUrl: string;
  projectUrl: string;
}

const ProjectCard: React.FC<ProjectProps & { isHero?: boolean; height?: string }> = ({
  title,
  category,
  location,
  imageUrl,
  projectUrl,
  isHero = false,
  height = "h-64"
}) => {
  const [imageSrc, setImageSrc] = useState(imageUrl || '/gallery/court1.jpg');
  const [imageError, setImageError] = useState(false);

  // Fallback image if the original fails to load
  const handleImageError = () => {
    if (!imageError) {
      setImageError(true);
      setImageSrc('/gallery/court1.jpg'); // Use a default image from your gallery
    }
  };

  return (
    <div className={`group relative overflow-hidden rounded-lg shadow-lg hover:shadow-2xl transition-all duration-500 ${height} ${isHero ? 'row-span-2' : ''}`}>
      <Link href={projectUrl || '#'}>
        <div className="relative w-full h-full">
          <Image
            src={imageSrc}
            alt={title}
            fill
            className="object-cover object-center transition-transform duration-700 group-hover:scale-110"
            onError={handleImageError}
            sizes={isHero ? "(max-width: 768px) 100vw, 50vw" : "(max-width: 768px) 100vw, 25vw"}
            style={{ objectFit: 'cover', objectPosition: 'center' }}
          />

          {/* Gradient Overlay */}
          <div className="absolute inset-0 bg-gradient-to-t from-black/80 via-black/20 to-transparent opacity-60 group-hover:opacity-80 transition-opacity duration-500"></div>

          {/* Content Overlay */}
          <div className="absolute bottom-0 left-0 right-0 p-4 md:p-6 text-white">
            <div className="mb-2">
              <span className="inline-block bg-brand-accent/90 text-white text-xs font-semibold px-3 py-1 rounded-full backdrop-blur-sm">
                {category}
              </span>
            </div>
            <h3 className={`font-bold text-white leading-tight group-hover:text-brand-accent transition-colors duration-300 ${isHero ? 'text-xl md:text-2xl' : 'text-base md:text-lg'}`}>
              {title}
            </h3>
            <p className={`text-gray-200 mt-1 ${isHero ? 'text-sm md:text-base' : 'text-xs md:text-sm'}`}>
              {location}
            </p>
          </div>

          {/* Hover Effect Border */}
          <div className="absolute inset-0 border-2 border-brand-accent opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-lg"></div>
        </div>
      </Link>
    </div>
  );
};

interface FeaturedProjectsProps {
  projects: ProjectProps[];
}

const FeaturedProjects: React.FC<FeaturedProjectsProps> = ({ projects }) => {
  const [currentPage, setCurrentPage] = useState(0);
  const projectsPerPage = 6; // Show 6 projects: 1 hero + 5 regular
  const totalPages = Math.ceil(projects.length / projectsPerPage);

  const nextPage = () => {
    setCurrentPage((prev) => (prev + 1) % totalPages);
  };

  const prevPage = () => {
    setCurrentPage((prev) => (prev - 1 + totalPages) % totalPages);
  };

  const visibleProjects = projects.slice(
    currentPage * projectsPerPage,
    (currentPage + 1) * projectsPerPage
  );

  // Format the slide number with leading zero
  const formatSlideNumber = (num: number) => {
    return num < 10 ? `0${num}` : num;
  };

  return (
    <section className="py-16 bg-[#f8f7f4]">
      <div className="container mx-auto px-4 md:px-8">
        {/* Section Header */}
        <div className="flex items-center justify-between mb-12">
          <div className="flex items-center">
            <h2 className="text-2xl md:text-3xl font-bold text-brand-dark">Recent Projects</h2>
            <div className="flex-grow ml-6 h-px bg-gray-300 max-w-32"></div>
          </div>

          {/* Navigation Arrows - Desktop */}
          <div className="hidden md:flex items-center space-x-4">
            <button
              onClick={prevPage}
              className="w-12 h-12 rounded-full border-2 border-gray-300 hover:border-brand-accent text-gray-600 hover:text-brand-accent flex items-center justify-center transition-all duration-300 hover:shadow-lg"
              aria-label="Previous projects"
            >
              <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={2} stroke="currentColor" className="w-5 h-5">
                <path strokeLinecap="round" strokeLinejoin="round" d="M15.75 19.5L8.25 12l7.5-7.5" />
              </svg>
            </button>

            <button
              onClick={nextPage}
              className="w-12 h-12 rounded-full border-2 border-gray-300 hover:border-brand-accent text-gray-600 hover:text-brand-accent flex items-center justify-center transition-all duration-300 hover:shadow-lg"
              aria-label="Next projects"
            >
              <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={2} stroke="currentColor" className="w-5 h-5">
                <path strokeLinecap="round" strokeLinejoin="round" d="M8.25 4.5l7.5 7.5-7.5 7.5" />
              </svg>
            </button>
          </div>
        </div>

        {/* Projects Grid - Masonry Layout */}
        <div className="relative">
          <div className="grid grid-cols-1 md:grid-cols-4 lg:grid-cols-6 gap-4 md:gap-6 auto-rows-max">
            {visibleProjects.map((project, index) => {
              // First project is the hero (larger)
              if (index === 0) {
                return (
                  <div key={`${currentPage}-${index}`} className="md:col-span-2 lg:col-span-3">
                    <ProjectCard
                      title={project.title}
                      category={project.category}
                      location={project.location}
                      imageUrl={project.imageUrl}
                      projectUrl={project.projectUrl}
                      isHero={true}
                      height="h-80 md:h-96"
                    />
                  </div>
                );
              }

              // Remaining projects in staggered heights
              const heights = ['h-48', 'h-64', 'h-56', 'h-72', 'h-60'];
              const colSpans = ['md:col-span-1', 'md:col-span-1', 'lg:col-span-1', 'lg:col-span-2', 'lg:col-span-1'];

              return (
                <div key={`${currentPage}-${index}`} className={`${colSpans[(index - 1) % colSpans.length]} ${index <= 2 ? 'lg:col-span-1' : ''}`}>
                  <ProjectCard
                    title={project.title}
                    category={project.category}
                    location={project.location}
                    imageUrl={project.imageUrl}
                    projectUrl={project.projectUrl}
                    height={heights[(index - 1) % heights.length]}
                  />
                </div>
              );
            })}
          </div>

          {/* Mobile Navigation */}
          <div className="md:hidden mt-8 flex justify-center space-x-4">
            <button
              onClick={prevPage}
              className="w-12 h-12 rounded-full border-2 border-gray-300 hover:border-brand-accent text-gray-600 hover:text-brand-accent flex items-center justify-center transition-all duration-300"
              aria-label="Previous projects"
            >
              <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={2} stroke="currentColor" className="w-5 h-5">
                <path strokeLinecap="round" strokeLinejoin="round" d="M15.75 19.5L8.25 12l7.5-7.5" />
              </svg>
            </button>

            <button
              onClick={nextPage}
              className="w-12 h-12 rounded-full border-2 border-gray-300 hover:border-brand-accent text-gray-600 hover:text-brand-accent flex items-center justify-center transition-all duration-300"
              aria-label="Next projects"
            >
              <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={2} stroke="currentColor" className="w-5 h-5">
                <path strokeLinecap="round" strokeLinejoin="round" d="M8.25 4.5l7.5 7.5-7.5 7.5" />
              </svg>
            </button>
          </div>

          {/* Page Indicator */}
          {/* Page Indicator */}
          <div className="mt-8 flex justify-center items-center space-x-3">
            <span className="text-lg font-semibold text-brand-dark">{formatSlideNumber(currentPage + 1)}</span>
            <div className="w-16 h-px bg-gray-400"></div>
            <span className="text-lg text-gray-500">{formatSlideNumber(totalPages)}</span>
          </div>
        </div>
      </div>
    </section>
  );
};

export default FeaturedProjects;














