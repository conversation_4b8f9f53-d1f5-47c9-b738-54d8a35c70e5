import React from 'react';
import ProjectCard from '@/components/ui/ProjectCard';
import CTABlock from '@/components/ui/CTABlock';
import { fetchSanityData } from '@/lib/sanity';
import { allProjectsQuery } from '@/lib/queries';
import { Project } from '@/types/sanity';
import { urlFor } from '@/lib/sanity';

// Make the component async to fetch data
export default async function ProjectsPage() {
  // Fetch projects from Sanity
  const projects = await fetchSanityData(allProjectsQuery) || [];
  
  // Fetch categories from Sanity but only if needed for filtering
  const categories = (await fetchSanityData('*[_type == "projectCategory"] | order(order asc) { name }'))?.map(cat => cat.name) || [
    "All Projects",
    "Basketball Courts",
    "Swimming Pools",
    "Playgrounds",
    "Soccer Fields",
    "Equipment & Gear",
    "Arena Design-Build"
  ];

  // Transform Sanity data to the format expected by ProjectCard
  const formattedProjects = projects.map((project: Project) => ({
    title: project.title,
    category: project.category,
    imageUrl: project.mainImage ? urlFor(project.mainImage).url() : '/images/placeholder-project.jpg',
    projectUrl: project.slug?.current ? `/projects/${project.slug.current}` : '#'
  }));

  // Fallback to sample data if no projects are returned, using actual images from your gallery
  const displayProjects = formattedProjects.length > 0 ? formattedProjects : [
    {
      title: "Kepler University Indoor Basketball Court",
      category: "Basketball Courts",
      imageUrl: "/gallery/k1.jpg",
      projectUrl: "/projects/kepler-university-indoor-basketball-court"
    },
    {
      title: "South Africa Jr NBA Indoor Court",
      category: "Basketball Courts",
      imageUrl: "/gallery/s1.jpg",
      projectUrl: "/projects/south-africa-jr-nba-indoor-court"
    },
    {
      title: "Giants of Africa Outdoor Basketball Court",
      category: "Basketball Courts",
      imageUrl: "/gallery/court1.jpg",
      projectUrl: "/projects/giants-of-africa-outdoor-basketball-court"
    },
    {
      title: "Angola Basketball Court for Jr NBA & Africell",
      category: "Basketball Courts",
      imageUrl: "/gallery/k2.jpg",
      projectUrl: "/projects/angola-basketball-court-jr-nba-africell"
    },
    {
      title: "CMU Outdoor Basketball Court",
      category: "Basketball Courts",
      imageUrl: "/gallery/k3.jpg",
      projectUrl: "/projects/cmu-outdoor-basketball-court"
    },
    {
      title: "Stecol Indoor Basketball Court",
      category: "Basketball Courts",
      imageUrl: "/gallery/k4.jpg",
      projectUrl: "/projects/stecol-indoor-basketball-court"
    },
    {
      title: "LDK Jr. NBA Indoor Basketball Court",
      category: "Basketball Courts",
      imageUrl: "/sports/LDK-Gymnasium-scaled.jpeg",
      projectUrl: "/projects/ldk-jr-nba-indoor-basketball-court"
    },
    {
      title: "IPRC HUYE Basketball Court",
      category: "Basketball Courts",
      imageUrl: "/gallery/k5.jpg",
      projectUrl: "/projects/iprc-huye-basketball-court"
    },
    {
      title: "Nyandungu Great Lake Complex Basketball Court",
      category: "Basketball Courts",
      imageUrl: "/gallery/k6.jpg",
      projectUrl: "/projects/nyandungu-great-lake-complex-basketball-court"
    },
    {
      title: "Central African Republic Basketball Federation Court",
      category: "Basketball Courts",
      imageUrl: "/gallery/k7.jpg",
      projectUrl: "/projects/central-african-republic-basketball-federation-court"
    },
    {
      title: "Swimming Pool Project",
      category: "Swimming Pools",
      imageUrl: "/gallery/swim-k1.jpg",
      projectUrl: "/projects/swimming-pool-project"
    },
    {
      title: "South Sudan Basketball Federation Indoor Basketball Court",
      category: "Basketball Courts",
      imageUrl: "/gallery/k8.jpg",
      projectUrl: "/projects/south-sudan-basketball-federation-indoor-basketball-court"
    }
  ];

  return (
    <div className="bg-white">
      {/* Projects Header */}
      <section className="py-20 px-4 sm:px-6 lg:px-8 bg-brand-offWhite">
        <div className="container mx-auto">
          <div className="max-w-3xl mx-auto text-center">
            <h1 className="text-4xl md:text-5xl font-bold mb-6">Our Projects</h1>
            <p className="text-xl text-brand-secondary">
              Explore our portfolio of completed sports facilities and see how we bring visions to life.
            </p>
          </div>
        </div>
      </section>

      {/* Projects Grid */}
      <section className="py-16 px-4 sm:px-6 lg:px-8">
        <div className="container mx-auto">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {displayProjects.map((project: {
              title: string;
              category: string;
              imageUrl: string;
              projectUrl: string;
            }, index: number) => (
              <ProjectCard
                key={index}
                title={project.title}
                category={project.category}
                imageUrl={project.imageUrl}
                projectUrl={project.projectUrl}
              />
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <CTABlock
        headline="Ready to Start Your Project?"
        description="Contact our team today to discuss how we can bring your sports facility vision to life."
        buttonText="Get in Touch"
        buttonLink="/contact"
        appearance="primary"
      />
    </div>
  );
}









