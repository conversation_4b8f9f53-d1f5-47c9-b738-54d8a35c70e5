'use client';

import React, { useState } from 'react';
import Image from 'next/image';
import Link from 'next/link';

interface ProjectProps {
  title: string;
  category: string;
  location: string;
  imageUrl: string;
  projectUrl: string;
}

const ProjectCard: React.FC<ProjectProps> = ({
  title,
  category,
  location,
  imageUrl,
  projectUrl
}) => {
  const [imageSrc, setImageSrc] = useState(imageUrl || '/gallery/court1.jpg');
  const [imageError, setImageError] = useState(false);

  // Fallback image if the original fails to load
  const handleImageError = () => {
    if (!imageError) {
      setImageError(true);
      setImageSrc('/gallery/court1.jpg'); // Use a default image from your gallery
    }
  };

  return (
    <div className="bg-white rounded-lg overflow-hidden shadow-sm hover:shadow-md transition-shadow duration-300 flex-shrink-0 w-64">
      <Link href={projectUrl || '#'}>
        <div className="group">
          {/* Image Container */}
          <div className="relative h-48 overflow-hidden">
            <Image
              src={imageSrc}
              alt={title}
              fill
              className="object-cover object-center transition-transform duration-500 group-hover:scale-105"
              onError={handleImageError}
              sizes="256px"
              style={{ objectFit: 'cover', objectPosition: 'center' }}
            />
          </div>

          {/* Content */}
          <div className="p-4">
            <h3 className="font-semibold text-gray-900 text-sm leading-tight mb-1 group-hover:text-brand-accent transition-colors">
              {title}
            </h3>
            <p className="text-gray-600 text-xs">
              {location}
            </p>
          </div>
        </div>
      </Link>
    </div>
  );
};

interface FeaturedProjectsProps {
  projects: ProjectProps[];
}

const FeaturedProjects: React.FC<FeaturedProjectsProps> = ({ projects }) => {
  const [currentPage, setCurrentPage] = useState(0);
  const projectsPerPage = 6; // Show 6 projects: 1 hero + 5 regular
  const totalPages = Math.ceil(projects.length / projectsPerPage);

  const nextPage = () => {
    setCurrentPage((prev) => (prev + 1) % totalPages);
  };

  const prevPage = () => {
    setCurrentPage((prev) => (prev - 1 + totalPages) % totalPages);
  };

  const visibleProjects = projects.slice(
    currentPage * projectsPerPage,
    (currentPage + 1) * projectsPerPage
  );

  // Format the slide number with leading zero
  const formatSlideNumber = (num: number) => {
    return num < 10 ? `0${num}` : num;
  };

  return (
    <section className="py-16 bg-[#f8f7f4]">
      <div className="container mx-auto px-4 md:px-8">
        {/* Section Header */}
        <div className="flex items-center justify-between mb-12">
          <div className="flex items-center">
            <h2 className="text-2xl md:text-3xl font-bold text-brand-dark">Recent Projects</h2>
            <div className="flex-grow ml-6 h-px bg-gray-300 max-w-32"></div>
          </div>

          {/* Navigation Arrows - Desktop */}
          <div className="hidden md:flex items-center space-x-4">
            <button
              onClick={prevPage}
              className="w-12 h-12 rounded-full border-2 border-gray-300 hover:border-brand-accent text-gray-600 hover:text-brand-accent flex items-center justify-center transition-all duration-300 hover:shadow-lg"
              aria-label="Previous projects"
            >
              <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={2} stroke="currentColor" className="w-5 h-5">
                <path strokeLinecap="round" strokeLinejoin="round" d="M15.75 19.5L8.25 12l7.5-7.5" />
              </svg>
            </button>

            <button
              onClick={nextPage}
              className="w-12 h-12 rounded-full border-2 border-gray-300 hover:border-brand-accent text-gray-600 hover:text-brand-accent flex items-center justify-center transition-all duration-300 hover:shadow-lg"
              aria-label="Next projects"
            >
              <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={2} stroke="currentColor" className="w-5 h-5">
                <path strokeLinecap="round" strokeLinejoin="round" d="M8.25 4.5l7.5 7.5-7.5 7.5" />
              </svg>
            </button>
          </div>
        </div>

        {/* Projects Grid - Masonry Layout */}
        <div className="relative">
          <div className="grid grid-cols-1 md:grid-cols-4 lg:grid-cols-6 gap-4 md:gap-6 auto-rows-max">
            {visibleProjects.map((project, index) => {
              // First project is the hero (larger)
              if (index === 0) {
                return (
                  <div key={`${currentPage}-${index}`} className="md:col-span-2 lg:col-span-3">
                    <ProjectCard
                      title={project.title}
                      category={project.category}
                      location={project.location}
                      imageUrl={project.imageUrl}
                      projectUrl={project.projectUrl}
                      isHero={true}
                      height="h-80 md:h-96"
                    />
                  </div>
                );
              }

              // Remaining projects in staggered heights
              const heights = ['h-48', 'h-64', 'h-56', 'h-72', 'h-60'];
              const colSpans = ['md:col-span-1', 'md:col-span-1', 'lg:col-span-1', 'lg:col-span-2', 'lg:col-span-1'];

              return (
                <div key={`${currentPage}-${index}`} className={`${colSpans[(index - 1) % colSpans.length]} ${index <= 2 ? 'lg:col-span-1' : ''}`}>
                  <ProjectCard
                    title={project.title}
                    category={project.category}
                    location={project.location}
                    imageUrl={project.imageUrl}
                    projectUrl={project.projectUrl}
                    height={heights[(index - 1) % heights.length]}
                  />
                </div>
              );
            })}
          </div>

          {/* Mobile Navigation */}
          <div className="md:hidden mt-8 flex justify-center space-x-4">
            <button
              onClick={prevPage}
              className="w-12 h-12 rounded-full border-2 border-gray-300 hover:border-brand-accent text-gray-600 hover:text-brand-accent flex items-center justify-center transition-all duration-300"
              aria-label="Previous projects"
            >
              <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={2} stroke="currentColor" className="w-5 h-5">
                <path strokeLinecap="round" strokeLinejoin="round" d="M15.75 19.5L8.25 12l7.5-7.5" />
              </svg>
            </button>

            <button
              onClick={nextPage}
              className="w-12 h-12 rounded-full border-2 border-gray-300 hover:border-brand-accent text-gray-600 hover:text-brand-accent flex items-center justify-center transition-all duration-300"
              aria-label="Next projects"
            >
              <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={2} stroke="currentColor" className="w-5 h-5">
                <path strokeLinecap="round" strokeLinejoin="round" d="M8.25 4.5l7.5 7.5-7.5 7.5" />
              </svg>
            </button>
          </div>

          {/* Page Indicator */}
          {/* Page Indicator */}
          <div className="mt-8 flex justify-center items-center space-x-3">
            <span className="text-lg font-semibold text-brand-dark">{formatSlideNumber(currentPage + 1)}</span>
            <div className="w-16 h-px bg-gray-400"></div>
            <span className="text-lg text-gray-500">{formatSlideNumber(totalPages)}</span>
          </div>
        </div>
      </div>
    </section>
  );
};

export default FeaturedProjects;














