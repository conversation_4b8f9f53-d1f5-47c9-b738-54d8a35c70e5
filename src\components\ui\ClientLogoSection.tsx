'use client';

import React, { useState } from 'react';
import Image from 'next/image';

export interface ClientLogoProps {
  name: string;
  logoUrl: string;
  grayscale?: boolean;
}

const ClientLogo: React.FC<ClientLogoProps> = ({
  name,
  logoUrl,
  grayscale = true
}) => {
  const [imageSrc, setImageSrc] = useState(logoUrl);
  const [imageError, setImageError] = useState(false);

  // Fallback if the original fails to load
  const handleImageError = () => {
    if (!imageError) {
      setImageError(true);
      setImageSrc('/clients/ferwaba.jpg'); // Use a default client logo
    }
  };

  return (
    <div className={`flex items-center justify-center p-4 ${grayscale ? 'grayscale hover:grayscale-0 transition-all duration-300' : ''}`}>
      <div className="client-logo">
        <Image
          src={imageSrc}
          alt={`${name} logo`}
          fill
          className="object-contain object-center p-2"
          onError={handleImageError}
          style={{ objectFit: 'contain', objectPosition: 'center' }}
        />
      </div>
    </div>
  );
};

interface ClientLogoSectionProps {
  headline?: string;
  logos: ClientLogoProps[];
}

const ClientLogoSection: React.FC<ClientLogoSectionProps> = ({
  headline,
  logos
}) => {
  return (
    <section className="py-12 px-4 sm:px-6 lg:px-8 bg-brand-white">
      <div className="container mx-auto">
        {headline && (
          <h2 className="text-xl font-medium text-brand-secondary text-center mb-8">
            {headline}
          </h2>
        )}
        
        <div className="flex flex-wrap justify-center items-center gap-4 md:gap-8">
          {logos.map((logo, index) => (
            <ClientLogo
              key={index}
              name={logo.name}
              logoUrl={logo.logoUrl}
              grayscale={logo.grayscale}
            />
          ))}
        </div>
      </div>
    </section>
  );
};

export default ClientLogoSection;
