import { createClient } from 'next-sanity';
import imageUrlBuilder from '@sanity/image-url';

export const client = createClient({
  projectId: 'ri3g78rr', // The project ID from the user
  dataset: 'production', // Default dataset
  apiVersion: '2023-05-03', // Use the latest API version
  useCdn: false, // We'll set to false for development, can be true for production
});

// Helper function to generate image URLs
const builder = imageUrlBuilder(client);
export const urlFor = (source: any) => builder.image(source);

// Helper function to fetch data with proper error handling
export async function fetchSanityData(query: string, params: Record<string, any> = {}) {
  try {
    return await client.fetch(query, params);
  } catch (error) {
    console.error('Error fetching Sanity data:', error);
    return null;
  }
}
