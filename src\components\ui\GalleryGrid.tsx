import React from 'react';
import Image from 'next/image';
import Link from 'next/link';

interface GalleryItemProps {
  imageUrl: string;
  title?: string;
  category?: string;
  projectUrl?: string;
}

const GalleryItem: React.FC<GalleryItemProps> = ({
  imageUrl,
  title,
  category,
  projectUrl
}) => {
  const content = (
    <div className="relative overflow-hidden rounded-lg group">
      {/* Image */}
      <div className="relative aspect-square">
        <Image
          src={imageUrl}
          alt={title || "Gallery image"}
          fill
          className="object-cover transition-transform duration-500 group-hover:scale-105"
        />
        
        {/* Overlay with info (only if title/category provided) */}
        {(title || category) && (
          <div className="absolute inset-0 bg-brand-dark bg-opacity-0 group-hover:bg-opacity-70 transition-all duration-300 flex items-center justify-center opacity-0 group-hover:opacity-100">
            <div className="text-center p-4">
              {category && (
                <span className="text-xs font-medium text-brand-accent bg-brand-dark bg-opacity-70 px-2 py-1 rounded-sm">
                  {category}
                </span>
              )}
              {title && (
                <h3 className="text-lg font-bold mt-2 text-brand-white">
                  {title}
                </h3>
              )}
            </div>
          </div>
        )}
      </div>
    </div>
  );
  
  // If projectUrl is provided, make the item clickable
  if (projectUrl) {
    return (
      <Link href={projectUrl} className="block">
        {content}
      </Link>
    );
  }
  
  // Otherwise, just return the content
  return content;
};

interface GalleryGridProps {
  items: GalleryItemProps[];
  columns?: number;
}

const GalleryGrid: React.FC<GalleryGridProps> = ({
  items,
  columns = 3
}) => {
  // Determine grid columns based on the columns prop
  const gridClass = {
    2: 'grid-cols-1 sm:grid-cols-2',
    3: 'grid-cols-1 sm:grid-cols-2 lg:grid-cols-3',
    4: 'grid-cols-1 sm:grid-cols-2 lg:grid-cols-4',
  }[columns] || 'grid-cols-1 sm:grid-cols-2 lg:grid-cols-3';
  
  return (
    <div className={`grid ${gridClass} gap-4 md:gap-6`}>
      {items.map((item, index) => (
        <GalleryItem
          key={index}
          imageUrl={item.imageUrl}
          title={item.title}
          category={item.category}
          projectUrl={item.projectUrl}
        />
      ))}
    </div>
  );
};

export default GalleryGrid;