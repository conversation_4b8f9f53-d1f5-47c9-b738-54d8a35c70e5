'use client';

import Link from 'next/link';
import { useEffect, useState } from 'react';
import Image from 'next/image';

const Header = () => {
  const [scrolled, setScrolled] = useState(false);
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);

    const handleScroll = () => {
      const isScrolled = window.scrollY > 10;
      if (isScrolled !== scrolled) {
        setScrolled(isScrolled);
      }
    };

    // Add scroll event listener
    window.addEventListener('scroll', handleScroll, { passive: true });

    // Call handler right away to update state with initial scroll position
    handleScroll();

    // Clean up
    return () => {
      window.removeEventListener('scroll', handleScroll);
    };
  }, [scrolled]);

  const navItems = [
    { name: 'Services', href: '/services' },
    { name: 'Portfolio', href: '/projects' },
    { name: 'About Us', href: '/about' },
    { name: 'Contact Us', href: '/contact' },
  ];

  // Prevent hydration mismatch by using consistent initial state
  const headerClasses = `header-black bg-black shadow-sm sticky top-0 z-50 w-full transition-all duration-300 ${
    mounted && scrolled ? 'shadow-md' : 'shadow-sm'
  }`;

  return (
    <header
      className={headerClasses}
      style={{
        backgroundColor: '#000000',
        borderBottom: '1px solid #333'
      }}
    >
      <nav className="container mx-auto px-4 sm:px-6 lg:px-8 flex items-center justify-between h-16">
        {/* Logo Placeholder */}
        <div className="flex-shrink-0">
          <Link href="/" className="text-2xl font-bold text-white">
            {/* Replace with SVG Logo Component later */}
            <Image src="/logo-white.png" alt="KefaSports" width={100} height={50} />
          </Link>
        </div>

        {/* Desktop Navigation */}
        <div className="hidden sm:ml-6 sm:flex sm:space-x-8">
          {navItems.map((item) => (
            <Link
              key={item.name}
              href={item.href}
              className="text-gray-300 hover:text-white inline-flex items-center px-1 pt-1 text-sm font-medium transition-colors duration-200"
            >
              {item.name}
            </Link>
          ))}
        </div>

        {/* CTA Button */}
        <div className="hidden sm:ml-6 sm:flex sm:items-center">
          <Link
            href="/contact" // Or a specific quote request page if created
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-brand-white bg-brand-accent hover:bg-opacity-90 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-brand-accent"
          >
            Request Quote
          </Link>
        </div>

        {/* Mobile Menu Button Placeholder - Implementation needed */}
        <div className="-mr-2 flex items-center sm:hidden">
          <button
            type="button"
            className="bg-black inline-flex items-center justify-center p-2 rounded-md text-gray-300 hover:text-white hover:bg-gray-800 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-brand-accent transition-colors duration-200"
            aria-controls="mobile-menu"
            aria-expanded="false"
          >
            <span className="sr-only">Open main menu</span>
            {/* Heroicon name: outline/menu */}
            <svg className="block h-6 w-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4 6h16M4 12h16M4 18h16" />
            </svg>
            {/* Heroicon name: outline/x */}
            <svg className="hidden h-6 w-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>
      </nav>

      {/* Mobile menu, show/hide based on menu state - Implementation needed */}
      {/* <div className="sm:hidden" id="mobile-menu">
        <div className="pt-2 pb-3 space-y-1">
          {navItems.map((item) => (
            <Link key={item.name} href={item.href} className="block pl-3 pr-4 py-2 border-l-4 border-transparent text-base font-medium text-brand-secondary hover:bg-gray-50 hover:border-gray-300 hover:text-brand-dark">
              {item.name}
            </Link>
          ))}
        </div>
        <div className="pt-4 pb-3 border-t border-gray-200">
          <div className="flex items-center px-4">
            <Link href="/contact" className="w-full inline-flex items-center justify-center px-4 py-2 border border-transparent rounded-md shadow-sm text-base font-medium text-brand-white bg-brand-accent hover:bg-opacity-90">
              Request Quote
            </Link>
          </div>
        </div>
      </div> */}
    </header>
  );
};

export default Header;



