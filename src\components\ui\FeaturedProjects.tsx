'use client';

import React, { useState } from 'react';
import Image from 'next/image';
import Link from 'next/link';

interface ProjectProps {
  title: string;
  category: string;
  location: string;
  imageUrl: string;
  projectUrl: string;
}

const ProjectCard: React.FC<ProjectProps> = ({ title, category, location, imageUrl, projectUrl }) => {
  const [imageSrc, setImageSrc] = useState(imageUrl || '/gallery/court1.jpg');
  const [imageError, setImageError] = useState(false);

  // Fallback image if the original fails to load
  const handleImageError = () => {
    if (!imageError) {
      setImageError(true);
      setImageSrc('/gallery/court1.jpg'); // Use a default image from your gallery
    }
  };

  return (
    <div className="group bg-white border border-gray-100">
      <Link href={projectUrl || '#'}>
        <div>
          <div className="featured-project-image">
            <Image
              src={imageSrc}
              alt={title}
              fill
              className="object-cover object-center"
              onError={handleImageError}
              sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 25vw"
              style={{ objectFit: 'cover', objectPosition: 'center' }}
            />
          </div>
          <div className="py-4 px-3">
            <h3 className="font-bold text-base">{title}</h3>
            <p className="text-xs text-gray-600">{location || category}</p>
          </div>
        </div>
      </Link>
    </div>
  );
};

interface FeaturedProjectsProps {
  projects: ProjectProps[];
}

const FeaturedProjects: React.FC<FeaturedProjectsProps> = ({ projects }) => {
  const [currentPage, setCurrentPage] = useState(0);
  const projectsPerPage = 4;
  const totalPages = Math.ceil(projects.length / projectsPerPage);
  
  const nextPage = () => {
    setCurrentPage((prev) => (prev + 1) % totalPages);
  };
  
  const prevPage = () => {
    setCurrentPage((prev) => (prev - 1 + totalPages) % totalPages);
  };
  
  const visibleProjects = projects.slice(
    currentPage * projectsPerPage, 
    (currentPage + 1) * projectsPerPage
  );

  // Format the slide number with leading zero
  const formatSlideNumber = (num: number) => {
    return num < 10 ? `0${num}` : num;
  };

  return (
    <section className="py-12 bg-[#f9f8f6]">
      <div className="container mx-auto">
        <div className="flex items-center mb-8 px-8">
          <h2 className="text-xl font-bold">Recent Projects</h2>
          <div className="flex-grow ml-4 h-px bg-gray-300"></div>
        </div>
        
        <div className="relative">
          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-1 px-8">
            {visibleProjects.map((project, index) => (
              <ProjectCard
                key={`${currentPage}-${index}`}
                title={project.title}
                category={project.category}
                location={project.location}
                imageUrl={project.imageUrl}
                projectUrl={project.projectUrl}
              />
            ))}
          </div>
          
          {/* New navigation bar at bottom */}
          <div className="mt-8 bg-gray-100 py-4 px-8 flex justify-between items-center">
            <div className="flex items-center space-x-2">
              <span className="font-medium">{formatSlideNumber(currentPage + 1)}</span>
              <div className="w-12 h-px bg-gray-300"></div>
              <span className="text-gray-400">{formatSlideNumber(totalPages)}</span>
            </div>
            
            <div className="flex items-center space-x-2">
              <button 
                onClick={prevPage}
                className="w-10 h-10 rounded-full bg-white flex items-center justify-center border border-gray-200 hover:bg-gray-50"
                aria-label="Previous projects"
              >
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="w-5 h-5">
                  <path strokeLinecap="round" strokeLinejoin="round" d="M15.75 19.5L8.25 12l7.5-7.5" />
                </svg>
              </button>
              
              <button 
                onClick={nextPage}
                className="w-10 h-10 rounded-full bg-brand-accent text-white flex items-center justify-center hover:bg-brand-dark transition-colors"
                aria-label="Next projects"
              >
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="w-5 h-5">
                  <path strokeLinecap="round" strokeLinejoin="round" d="M8.25 4.5l7.5 7.5-7.5 7.5" />
                </svg>
              </button>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default FeaturedProjects;














