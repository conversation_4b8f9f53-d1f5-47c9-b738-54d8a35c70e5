'use client';

import React, { useState } from 'react';
import Image from 'next/image';

export interface ClientLogoProps {
  name: string;
  logoUrl: string;
  grayscale?: boolean;
}

const ClientLogo: React.FC<ClientLogoProps> = ({
  name,
  logoUrl,
  grayscale = true
}) => {
  const [imageSrc, setImageSrc] = useState(logoUrl);
  const [imageError, setImageError] = useState(false);

  // Fallback if the original fails to load
  const handleImageError = () => {
    if (!imageError) {
      setImageError(true);
      setImageSrc('/clients/ferwaba.jpg'); // Use a default client logo
    }
  };

  return (
    <div className="group relative">
      {/* Logo Container with Professional Styling */}
      <div className="relative bg-white rounded-xl shadow-md hover:shadow-xl transition-all duration-500 p-6 md:p-8 border border-gray-100 hover:border-brand-accent/20 transform hover:-translate-y-1">
        {/* Logo Image */}
        <div className="relative h-16 md:h-20 w-full">
          <Image
            src={imageSrc}
            alt={`${name} logo`}
            fill
            className={`object-contain object-center transition-all duration-500 ${
              grayscale
                ? 'grayscale opacity-70 group-hover:grayscale-0 group-hover:opacity-100'
                : 'opacity-90 group-hover:opacity-100'
            }`}
            onError={handleImageError}
            style={{ objectFit: 'contain', objectPosition: 'center' }}
          />
        </div>

        {/* Client Name (Hidden by default, shown on hover) */}
        <div className="absolute inset-x-0 bottom-0 bg-brand-dark bg-opacity-95 text-white text-xs font-medium text-center py-2 rounded-b-xl opacity-0 group-hover:opacity-100 transition-opacity duration-300">
          {name}
        </div>

        {/* Subtle accent border on hover */}
        <div className="absolute inset-0 rounded-xl border-2 border-brand-accent opacity-0 group-hover:opacity-20 transition-opacity duration-300"></div>
      </div>
    </div>
  );
};

interface ClientLogoSectionProps {
  headline?: string;
  logos: ClientLogoProps[];
  subtitle?: string;
}

const ClientLogoSection: React.FC<ClientLogoSectionProps> = ({
  headline,
  logos,
  subtitle
}) => {
  return (
    <section className="py-20 px-4 sm:px-6 lg:px-8 bg-gradient-to-br from-gray-50 to-white relative overflow-hidden">
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-5">
        <div className="absolute inset-0" style={{
          backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23000000' fill-opacity='0.1'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`,
        }}></div>
      </div>

      <div className="container mx-auto relative">
        {/* Section Header */}
        <div className="text-center mb-16">
          {headline && (
            <h2 className="text-3xl md:text-4xl font-bold text-brand-dark mb-4">
              {headline}
            </h2>
          )}
          {subtitle && (
            <p className="text-lg text-brand-secondary max-w-2xl mx-auto">
              {subtitle}
            </p>
          )}
          {!subtitle && (
            <p className="text-lg text-brand-secondary max-w-2xl mx-auto">
              We're proud to partner with leading organizations across Africa and beyond
            </p>
          )}

          {/* Decorative Line */}
          <div className="flex items-center justify-center mt-8">
            <div className="h-px bg-gradient-to-r from-transparent via-brand-accent to-transparent w-24"></div>
            <div className="mx-4 w-2 h-2 bg-brand-accent rounded-full"></div>
            <div className="h-px bg-gradient-to-r from-transparent via-brand-accent to-transparent w-24"></div>
          </div>
        </div>

        {/* Logos Grid */}
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6 md:gap-8 max-w-6xl mx-auto">
          {logos.map((logo, index) => (
            <div
              key={index}
              className="flex justify-center"
              style={{
                animationDelay: `${index * 100}ms`
              }}
            >
              <ClientLogo
                name={logo.name}
                logoUrl={logo.logoUrl}
                grayscale={logo.grayscale}
              />
            </div>
          ))}
        </div>

        {/* Trust Indicators */}
        <div className="mt-16 text-center">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-4xl mx-auto">
            <div className="flex flex-col items-center">
              <div className="w-12 h-12 bg-brand-accent rounded-full flex items-center justify-center mb-4">
                <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
              <h3 className="text-lg font-semibold text-brand-dark mb-2">Proven Track Record</h3>
              <p className="text-brand-secondary text-sm">50+ successful projects delivered on time and on budget</p>
            </div>

            <div className="flex flex-col items-center">
              <div className="w-12 h-12 bg-brand-accent rounded-full flex items-center justify-center mb-4">
                <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                </svg>
              </div>
              <h3 className="text-lg font-semibold text-brand-dark mb-2">Regional Expertise</h3>
              <p className="text-brand-secondary text-sm">Serving clients across 15+ countries in Africa</p>
            </div>

            <div className="flex flex-col items-center">
              <div className="w-12 h-12 bg-brand-accent rounded-full flex items-center justify-center mb-4">
                <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
                </svg>
              </div>
              <h3 className="text-lg font-semibold text-brand-dark mb-2">Client Satisfaction</h3>
              <p className="text-brand-secondary text-sm">100% client satisfaction with long-term partnerships</p>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default ClientLogoSection;
