import React from 'react';
import SectionHeading from '@/components/ui/SectionHeading';
import GalleryGrid from '@/components/ui/GalleryGrid';
import CTABlock from '@/components/ui/CTABlock';

export default function GalleryPage() {
  // Sample data - would be fetched from Sanity CMS in production
  const galleryItems = [
    {
      imageUrl: "/images/placeholder-project-1.jpg",
      title: "University Basketball Court",
      category: "Basketball Courts",
      projectUrl: "/projects/university-athletic-complex"
    },
    {
      imageUrl: "/images/placeholder-project-2.jpg",
      title: "Community Swimming Pool",
      category: "Swimming Pools",
      projectUrl: "/projects/community-aquatic-center"
    },
    {
      imageUrl: "/images/placeholder-project-3.jpg",
      title: "Municipal Soccer Field",
      category: "Soccer Fields",
      projectUrl: "/projects/municipal-sports-park"
    },
    {
      imageUrl: "/images/placeholder-project-4.jpg",
      title: "Elementary School Playground",
      category: "Playgrounds",
      projectUrl: "/projects/elementary-school-playground"
    },
    {
      imageUrl: "/images/placeholder-project-5.jpg",
      title: "Regional Sports Arena",
      category: "Arena Design-Build",
      projectUrl: "/projects/regional-sports-arena"
    },
    {
      imageUrl: "/images/placeholder-project-6.jpg",
      title: "College Recreation Center",
      category: "Equipment & Gear",
      projectUrl: "/projects/college-recreation-center"
    },
    {
      imageUrl: "/images/placeholder-project-7.jpg",
      title: "High School Gymnasium",
      category: "Basketball Courts"
    },
    {
      imageUrl: "/images/placeholder-project-8.jpg",
      title: "Community Park Playground",
      category: "Playgrounds"
    },
    {
      imageUrl: "/images/placeholder-project-9.jpg",
      title: "Olympic Training Pool",
      category: "Swimming Pools"
    },
    {
      imageUrl: "/images/placeholder-project-10.jpg",
      title: "Professional Soccer Stadium",
      category: "Soccer Fields"
    },
    {
      imageUrl: "/images/placeholder-project-11.jpg",
      title: "Youth Sports Complex",
      category: "Arena Design-Build"
    },
    {
      imageUrl: "/images/placeholder-project-12.jpg",
      title: "Fitness Center Equipment",
      category: "Equipment & Gear"
    }
  ];

  // Categories for filter
  const categories = [
    "All",
    "Basketball Courts",
    "Swimming Pools",
    "Playgrounds",
    "Soccer Fields",
    "Equipment & Gear",
    "Arena Design-Build"
  ];

  return (
    <div className="bg-brand-white">
      {/* Page Header */}
      <div className="bg-brand-dark text-brand-white py-16 px-4 sm:px-6 lg:px-8">
        <div className="container mx-auto max-w-4xl text-center">
          <h1 className="text-4xl md:text-5xl font-bold mb-4">Gallery</h1>
          <p className="text-xl text-brand-lighterGray">
            Visual showcase of our completed sports facilities and projects.
          </p>
        </div>
      </div>

      {/* Filter Section */}
      <section className="py-8 px-4 sm:px-6 lg:px-8 bg-brand-offWhite">
        <div className="container mx-auto">
          <div className="flex flex-wrap justify-center gap-4">
            {categories.map((category, index) => (
              <button
                key={index}
                className={`px-4 py-2 rounded-md text-sm font-medium ${
                  index === 0 
                    ? 'bg-brand-accent text-brand-white' 
                    : 'bg-brand-white text-brand-secondary hover:bg-brand-accent hover:text-brand-white'
                } transition-colors`}
              >
                {category}
              </button>
            ))}
          </div>
        </div>
      </section>

      {/* Gallery Grid */}
      <section className="py-16 px-4 sm:px-6 lg:px-8">
        <div className="container mx-auto">
          <GalleryGrid items={galleryItems} columns={3} />
        </div>
      </section>

      {/* CTA Section */}
      <CTABlock
        headline="Inspired by What You See?"
        description="Contact us to discuss how we can create a similar high-quality sports facility for your organization."
        buttonText="Start Your Project"
        buttonLink="/contact"
        appearance="primary"
      />
    </div>
  );
}
